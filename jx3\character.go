package jx3

import (
	"fmt"
	"image"

	"github.com/user/jx3auto/utils"
)

// CheckCharacter 检查是否有创建的角色
func (c *Client) CheckCharacter() (bool, error) {
	// 确保已经在角色选择界面
	if !c.<PERSON><PERSON><PERSON>haracterScreen() {
		return false, fmt.Errorf("角色选择界面加载失败")
	}

	// 获取角色区域
	rect := image.Rect(
		c.Config.ScreenCoord.CharacterArea.X,
		c.Config.ScreenCoord.CharacterArea.Y,
		c.Config.ScreenCoord.CharacterArea.X+c.Config.ScreenCoord.CharacterArea.Width,
		c.Config.ScreenCoord.CharacterArea.Y+c.Config.ScreenCoord.CharacterArea.Height,
	)

	// 检查角色区域是否有角色
	hasCharacter := utils.HasCharacter(rect)

	if hasCharacter {
		fmt.Println("检测到已创建角色")
	} else {
		fmt.Println("未检测到已创建角色")
	}

	// 截图保存角色区域，用于调试
	img := utils.CaptureRect(
		c.Config.ScreenCoord.CharacterArea.X,
		c.Config.ScreenCoord.CharacterArea.Y,
		c.Config.ScreenCoord.CharacterArea.Width,
		c.Config.ScreenCoord.CharacterArea.Height,
	)
	utils.SaveImage(img, "character_area.png")

	return hasCharacter, nil
}

// GetCharacterCount 获取角色数量
func (c *Client) GetCharacterCount() (int, error) {
	// 实际应用中，应该使用图像识别来计算角色数量
	// 这里简化处理，只返回是否有角色
	hasCharacter, err := c.CheckCharacter()
	if err != nil {
		return 0, err
	}

	if hasCharacter {
		// 假设只有一个角色
		return 1, nil
	}
	return 0, nil
}
