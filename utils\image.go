package utils

import (
	"image"
	"image/color"
	"os"

	"github.com/go-vgo/robotgo"
	"github.com/vcaesar/gcv"
)

// CaptureScreen 截取屏幕
func CaptureScreen() image.Image {
	bitmap := robotgo.CaptureScreen()
	defer robotgo.FreeBitmap(bitmap)
	return robotgo.ToImage(bitmap)
}

// CaptureRect 截取屏幕指定区域
func CaptureRect(x, y, width, height int) image.Image {
	bitmap := robotgo.CaptureScreen(x, y, width, height)
	defer robotgo.FreeBitmap(bitmap)
	return robotgo.ToImage(bitmap)
}

// SaveImage 保存图像到文件
func SaveImage(img image.Image, path string) error {
	return robotgo.SavePng(path, img)
}

// FindImage 在屏幕上查找图像
func FindImage(targetPath string) (int, int, bool) {
	fx, fy := robotgo.FindPic(targetPath)
	if fx == -1 && fy == -1 {
		return fx, fy, false
	}
	return fx, fy, true
}

// FindImageInRegion 在屏幕指定区域查找图像
func FindImageInRegion(targetPath string, x, y, width, height int) (int, int, bool) {
	fx, fy := robotgo.FindPic(targetPath, robotgo.Rectangle{X: x, Y: y, W: width, H: height})
	if fx == -1 && fy == -1 {
		return fx, fy, false
	}
	return fx, fy, true
}

// CompareImages 比较两个图像的相似度
func CompareImages(img1, img2 image.Image) float64 {
	return gcv.CompareImg(img1, img2)
}

// LoadImage 从文件加载图像
func LoadImage(path string) (image.Image, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	
	img, _, err := image.Decode(file)
	return img, err
}

// HasCharacter 检查角色区域是否有角色
// 通过检查角色区域的颜色分布来判断是否有角色
func HasCharacter(rect image.Rectangle) bool {
	img := CaptureRect(rect.Min.X, rect.Min.Y, rect.Dx(), rect.Dy())
	
	// 计算非背景色像素的数量
	// 这里假设背景是暗色的，角色会有较亮的颜色
	threshold := 100 // 亮度阈值
	nonBackgroundPixels := 0
	totalPixels := rect.Dx() * rect.Dy()
	
	bounds := img.Bounds()
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			r, g, b, _ := img.At(x, y).RGBA()
			// 转换为0-255范围
			r8 := uint8(r >> 8)
			g8 := uint8(g >> 8)
			b8 := uint8(b >> 8)
			
			// 计算亮度
			brightness := (0.299*float64(r8) + 0.587*float64(g8) + 0.114*float64(b8))
			
			if brightness > float64(threshold) {
				nonBackgroundPixels++
			}
		}
	}
	
	// 如果非背景像素占比超过10%，认为有角色
	ratio := float64(nonBackgroundPixels) / float64(totalPixels)
	return ratio > 0.1
}
