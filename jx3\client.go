package jx3

import (
	"fmt"
	"os"
	"os/exec"
	"time"

	"github.com/user/jx3auto/config"
	"github.com/user/jx3auto/utils"
)

// Client 剑网三客户端
type Client struct {
	Config *config.Config
	cmd    *exec.Cmd
}

// NewClient 创建新的剑网三客户端
func NewClient(cfg *config.Config) *Client {
	return &Client{
		Config: cfg,
	}
}

// Start 启动剑网三客户端
func (c *Client) Start() error {
	// 检查游戏路径是否存在
	if _, err := os.Stat(c.Config.GamePath); os.IsNotExist(err) {
		return fmt.Errorf("游戏路径不存在: %s", c.Config.GamePath)
	}

	// 启动游戏
	c.cmd = exec.Command(c.Config.GamePath)
	err := c.cmd.Start()
	if err != nil {
		return fmt.Errorf("启动游戏失败: %v", err)
	}

	fmt.Println("游戏启动中，请等待...")
	// 等待游戏启动
	utils.Sleep(30)

	return nil
}

// Close 关闭剑网三客户端
func (c *Client) Close() error {
	if c.cmd != nil && c.cmd.Process != nil {
		return c.cmd.Process.Kill()
	}
	return nil
}

// WaitForLoginScreen 等待登录界面出现
func (c *Client) WaitForLoginScreen() bool {
	fmt.Println("等待登录界面...")
	
	// 这里应该使用图像识别来确认登录界面已经加载
	// 简化起见，我们只是等待固定时间
	utils.Sleep(10)
	
	// 检查登录按钮是否可见
	// 实际应用中，应该使用图像识别来确认
	return true
}

// WaitForServerScreen 等待服务器选择界面出现
func (c *Client) WaitForServerScreen() bool {
	fmt.Println("等待服务器选择界面...")
	
	// 等待固定时间
	utils.Sleep(5)
	
	return true
}

// WaitForCharacterScreen 等待角色选择界面出现
func (c *Client) WaitForCharacterScreen() bool {
	fmt.Println("等待角色选择界面...")
	
	// 等待固定时间
	utils.Sleep(10)
	
	return true
}

// IsRunning 检查游戏是否正在运行
func (c *Client) IsRunning() bool {
	if c.cmd == nil || c.cmd.Process == nil {
		return false
	}
	
	// 尝试获取进程状态
	process, err := os.FindProcess(c.cmd.Process.Pid)
	if err != nil {
		return false
	}
	
	// 在Windows上，FindProcess总是成功，所以我们需要额外检查
	// 在实际应用中，可以使用更复杂的方法检查进程是否存在
	return process != nil
}
