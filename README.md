# 剑网三自动登录检查角色程序

这是一个用Go语言编写的剑网三自动登录并检查是否有创建角色的程序。

## 功能

- 自动启动剑网三客户端
- 自动登录指定账号
- 自动选择服务器
- 检查是否有已创建的角色
- 支持多账号批量处理

## 依赖

- Go 1.21 或更高版本
- github.com/go-vgo/robotgo - 用于模拟鼠标和键盘操作
- github.com/vcaesar/gcv - 用于图像处理

## 安装

1. 克隆仓库：

```bash
git clone https://github.com/your-username/jx3auto.git
cd jx3auto
```

2. 安装依赖：

```bash
go mod tidy
```

## 配置

首次运行时，创建默认配置文件：

```bash
go run main.go -create-config
```

这将创建一个`config.json`文件，你需要编辑它来设置：

1. 游戏客户端路径
2. 账号信息（用户名、密码、服务器）
3. 屏幕坐标配置（根据你的屏幕分辨率调整）

配置文件示例：

```json
{
  "gamePath": "D:\\Games\\JX3\\JX3Client.exe",
  "accounts": [
    {
      "username": "your_username",
      "password": "your_password",
      "server": "蝶恋花"
    }
  ],
  "screenCoord": {
    "loginButton": {
      "x": 960,
      "y": 600
    },
    "usernameInput": {
      "x": 800,
      "y": 400
    },
    "passwordInput": {
      "x": 800,
      "y": 450
    },
    "serverButton": {
      "x": 800,
      "y": 500
    },
    "enterGameBtn": {
      "x": 960,
      "y": 700
    },
    "characterArea": {
      "x": 500,
      "y": 300,
      "width": 920,
      "height": 400
    }
  }
}
```

## 使用方法

运行程序：

```bash
go run main.go
```

或者指定配置文件路径：

```bash
go run main.go -config path/to/config.json
```

## 注意事项

1. 程序运行时，请不要移动鼠标或使用键盘，以免干扰自动操作
2. 屏幕坐标需要根据你的屏幕分辨率和游戏界面进行调整
3. 程序会在角色选择界面截图并保存为`character_area.png`，用于调试
4. 如果游戏界面发生变化，可能需要更新屏幕坐标配置

## 自定义

如果需要适应不同的游戏界面或添加更多功能，可以修改以下文件：

- `jx3/client.go` - 剑网三客户端操作
- `jx3/login.go` - 登录相关功能
- `jx3/character.go` - 角色检查相关功能
- `utils/image.go` - 图像识别工具
- `utils/input.go` - 键盘鼠标输入工具

## 许可证

MIT
