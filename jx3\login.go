package jx3

import (
	"fmt"
	"time"

	"github.com/user/jx3auto/config"
	"github.com/user/jx3auto/utils"
)

// Login 登录剑网三
func (c *Client) Login(account config.Account) error {
	// 等待登录界面
	if !c.WaitForLoginScreen() {
		return fmt.Errorf("登录界面加载超时")
	}

	fmt.Printf("开始登录账号: %s\n", account.Username)

	// 点击用户名输入框
	utils.MouseClick(c.Config.ScreenCoord.UsernameInput.X, c.Config.ScreenCoord.UsernameInput.Y)
	utils.Sleep(1)

	// 输入用户名
	utils.ClearAndType(account.Username)
	utils.Sleep(1)

	// 点击密码输入框
	utils.MouseClick(c.Config.ScreenCoord.PasswordInput.X, c.Config.ScreenCoord.PasswordInput.Y)
	utils.Sleep(1)

	// 输入密码
	utils.ClearAndType(account.Password)
	utils.Sleep(1)

	// 点击登录按钮
	utils.MouseClick(c.Config.ScreenCoord.LoginButton.X, c.Config.ScreenCoord.LoginButton.Y)
	utils.Sleep(5)

	// 等待服务器选择界面
	if !c.WaitForServerScreen() {
		return fmt.Errorf("服务器选择界面加载超时")
	}

	// 选择服务器
	err := c.SelectServer(account.Server)
	if err != nil {
		return err
	}

	// 点击进入游戏按钮
	utils.MouseClick(c.Config.ScreenCoord.EnterGameBtn.X, c.Config.ScreenCoord.EnterGameBtn.Y)
	utils.Sleep(2)

	// 等待角色选择界面
	if !c.WaitForCharacterScreen() {
		return fmt.Errorf("角色选择界面加载超时")
	}

	fmt.Println("登录成功，已进入角色选择界面")
	return nil
}

// SelectServer 选择服务器
func (c *Client) SelectServer(serverName string) error {
	// 点击服务器选择按钮
	utils.MouseClick(c.Config.ScreenCoord.ServerButton.X, c.Config.ScreenCoord.ServerButton.Y)
	utils.Sleep(2)

	// 实际应用中，应该使用图像识别来查找并点击指定的服务器
	// 这里简化处理，假设已经选择了正确的服务器
	fmt.Printf("选择服务器: %s\n", serverName)
	
	// 模拟点击服务器名称
	// 这里需要根据实际界面调整坐标
	utils.MouseClick(c.Config.ScreenCoord.ServerButton.X, c.Config.ScreenCoord.ServerButton.Y + 50)
	utils.Sleep(2)

	return nil
}
