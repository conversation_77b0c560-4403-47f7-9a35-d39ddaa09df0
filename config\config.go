package config

import (
	"encoding/json"
	"os"
	"path/filepath"
)

// Config 存储程序配置
type Config struct {
	GamePath    string     `json:"gamePath"`    // 剑网三客户端路径
	Accounts    []Account  `json:"accounts"`    // 账号列表
	ScreenCoord Coordinate `json:"screenCoord"` // 屏幕坐标配置
}

// Account 存储账号信息
type Account struct {
	Username string `json:"username"` // 用户名
	Password string `json:"password"` // 密码
	Server   string `json:"server"`   // 服务器
}

// Coordinate 存储UI元素坐标
type Coordinate struct {
	LoginButton    Point `json:"loginButton"`    // 登录按钮坐标
	UsernameInput  Point `json:"usernameInput"`  // 用户名输入框坐标
	PasswordInput  Point `json:"passwordInput"`  // 密码输入框坐标
	ServerButton   Point `json:"serverButton"`   // 服务器选择按钮坐标
	EnterGameBtn   Point `json:"enterGameBtn"`   // 进入游戏按钮坐标
	CharacterArea  Rect  `json:"characterArea"`  // 角色区域
}

// Point 表示屏幕上的一个点
type Point struct {
	X int `json:"x"`
	Y int `json:"y"`
}

// Rect 表示屏幕上的一个矩形区域
type Rect struct {
	X      int `json:"x"`
	Y      int `json:"y"`
	Width  int `json:"width"`
	Height int `json:"height"`
}

// LoadConfig 从文件加载配置
func LoadConfig(path string) (*Config, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	var config Config
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

// SaveConfig 保存配置到文件
func SaveConfig(config *Config, path string) error {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	dir := filepath.Dir(path)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return err
		}
	}

	return os.WriteFile(path, data, 0644)
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		GamePath: "D:\\Games\\JX3\\JX3Client.exe",
		Accounts: []Account{
			{
				Username: "your_username",
				Password: "your_password",
				Server:   "蝶恋花",
			},
		},
		ScreenCoord: Coordinate{
			LoginButton: Point{X: 960, Y: 600},
			UsernameInput: Point{X: 800, Y: 400},
			PasswordInput: Point{X: 800, Y: 450},
			ServerButton: Point{X: 800, Y: 500},
			EnterGameBtn: Point{X: 960, Y: 700},
			CharacterArea: Rect{X: 500, Y: 300, Width: 920, Height: 400},
		},
	}
}
