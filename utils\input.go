package utils

import (
	"time"

	"github.com/go-vgo/robotgo"
)

// MouseClick 模拟鼠标点击
func MouseClick(x, y int) {
	robotgo.MoveClick(x, y, "left")
}

// MouseMove 移动鼠标到指定位置
func MouseMove(x, y int) {
	robotgo.MoveMouse(x, y)
}

// TypeString 模拟键盘输入字符串
func TypeString(text string) {
	robotgo.TypeStr(text)
}

// PressKey 模拟按下键盘按键
func PressKey(key string) {
	robotgo.KeyTap(key)
}

// PressKeyWithModifier 模拟按下带修饰符的键盘按键
func PressKeyWithModifier(key string, modifier string) {
	robotgo.KeyTap(key, modifier)
}

// Sleep 等待指定时间
func Sleep(seconds int) {
	time.Sleep(time.Duration(seconds) * time.Second)
}

// ClearAndType 清除输入框并输入新内容
func ClearAndType(text string) {
	// 全选当前内容
	PressKeyWithModifier("a", "ctrl")
	// 删除选中内容
	PressKey("delete")
	// 输入新内容
	TypeString(text)
}
