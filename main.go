package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"

	"github.com/user/jx3auto/config"
	"github.com/user/jx3auto/jx3"
)

func main() {
	// 解析命令行参数
	configPath := flag.String("config", "config.json", "配置文件路径")
	createConfig := flag.Bool("create-config", false, "创建默认配置文件")
	flag.Parse()

	// 创建默认配置文件
	if *createConfig {
		cfg := config.DefaultConfig()
		err := config.SaveConfig(cfg, *configPath)
		if err != nil {
			fmt.Printf("创建配置文件失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("默认配置文件已创建: %s\n", *configPath)
		fmt.Println("请编辑配置文件，设置正确的游戏路径、账号信息和屏幕坐标")
		os.Exit(0)
	}

	// 加载配置文件
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		if os.IsNotExist(err) {
			fmt.Printf("配置文件不存在: %s\n", *configPath)
			fmt.Println("使用 -create-config 参数创建默认配置文件")
			os.Exit(1)
		}
		fmt.Printf("加载配置文件失败: %v\n", err)
		os.Exit(1)
	}

	// 创建剑网三客户端
	client := jx3.NewClient(cfg)

	// 启动游戏
	fmt.Println("正在启动剑网三客户端...")
	err = client.Start()
	if err != nil {
		fmt.Printf("启动游戏失败: %v\n", err)
		os.Exit(1)
	}

	// 确保程序退出时关闭游戏
	defer func() {
		fmt.Println("正在关闭剑网三客户端...")
		if err := client.Close(); err != nil {
			fmt.Printf("关闭游戏失败: %v\n", err)
		}
	}()

	// 遍历账号列表
	for i, account := range cfg.Accounts {
		fmt.Printf("\n[账号 %d/%d] 开始处理账号: %s\n", i+1, len(cfg.Accounts), account.Username)

		// 登录账号
		err = client.Login(account)
		if err != nil {
			fmt.Printf("登录失败: %v\n", err)
			continue
		}

		// 检查是否有角色
		hasCharacter, err := client.CheckCharacter()
		if err != nil {
			fmt.Printf("检查角色失败: %v\n", err)
			continue
		}

		// 输出结果
		if hasCharacter {
			fmt.Printf("账号 %s 在服务器 %s 上有已创建的角色\n", account.Username, account.Server)
		} else {
			fmt.Printf("账号 %s 在服务器 %s 上没有已创建的角色\n", account.Username, account.Server)
		}

		// 如果有多个账号，需要退出当前账号
		if i < len(cfg.Accounts)-1 {
			fmt.Println("退出当前账号，准备登录下一个账号...")
			// 按ESC键退出到登录界面
			// 实际应用中，应该根据游戏界面状态执行不同的退出操作
			// 这里简化处理，假设按ESC键可以退回到登录界面
			// utils.PressKey("escape")
			// utils.Sleep(5)
			
			// 重启游戏客户端
			client.Close()
			utils.Sleep(5)
			client.Start()
		}
	}

	fmt.Println("\n所有账号处理完成")
}

// 创建配置目录
func ensureConfigDir(path string) error {
	dir := filepath.Dir(path)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return os.MkdirAll(dir, 0755)
	}
	return nil
}
